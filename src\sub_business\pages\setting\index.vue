<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="设置"></CustomNavBar>
    </template>
    <view class="setting">
      <!-- <view class="setting-list flex-between border-b" @click="goPersonalData">
        <view class="text-32rpx">编辑个人资料</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view> -->
      <view class="setting-list flex-between" @click="goMessageSettings">
        <view class="text-32rpx color-333">消息设置</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <view class="setting-list flex-between" @click="goBlacklist">
        <view class="text-32rpx color-333">黑名单</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <view class="setting-list flex-between" @click="goAccountSecurity">
        <view class="text-32rpx color-333">账号信息</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <view class="setting-list flex-between border-b" @click="goPermissionManage">
        <view class="text-32rpx color-333">权限管理</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <view class="setting-list flex-between" @click="goGeneralSetup">
        <view class="text-32rpx color-333">清理缓存</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <view class="setting-list flex-between" @click="goLegalAgreement">
        <view class="text-32rpx color-333">法律协议</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <view class="setting-list border-b flex-between" @click="goInfoCollection">
        <view class="text-32rpx color-333">信息收集清单</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <view class="setting-list flex-between border-b" @click="goAboutUs">
        <view class="text-32rpx color-333">关于易直聘</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <view class="setting-list flex-between">
        <view class="text-32rpx color-333">版本更新</view>
        <view class="subText flex-c">
          <view class="p-r-10rpxrpx">0.0.80</view>
          <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view>
      <view class="setting-list border-b flex-between" @click="chanangeIdentity">
        <view class="text-32rpx color-333">切换身份</view>
        <view class="subText flex-c">
          <view class="p-r-10rpxrpx">{{ userRoleIsBusiness ? '伯乐' : '黑马' }}</view>
          <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view>
      <view class="btn-flexd">
        <view class="btn-flexd-red" @click="logoutBtm">退出登录</view>
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { logout } from '@/interPost/login'
import { clearStorageSync } from '@/utils/storage'
import { useLoginStore } from '@/store'
const { userRoleIsBusiness } = useUserInfo()
const { logoutEaseMobIM } = useEaseMobIM()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})

// 编辑个人资料
const goPersonalData = () => {
  uni.navigateTo({
    url: '/sub_business/pages/myInfo/index',
  })
}

// 消息设置
const goMessageSettings = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/model/message/MessageSettings',
  })
}

// 黑名单
const goBlacklist = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/model/blacklist/index',
  })
}

// 账号安全
const goAccountSecurity = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/model/accountInfo/index',
  })
}

// 权限管理
const goPermissionManage = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/model/permissionManage/index',
  })
}

// 清理缓存
const goGeneralSetup = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/model/clearCache/index',
  })
}

// 法律协议
const goLegalAgreement = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/model/PrivacyAgreement',
  })
}

// 个人信息收集清单
const goInfoCollection = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/model/InfoCollection/index',
  })
}

// 关于我们
const goAboutUs = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/model/aboutUs/index',
  })
}

// 退出登录
// vuex数据
const loginStore = useLoginStore()
const logoutBtm = async () => {
  const res: any = await logout()
  console.log(res, '--------')
  if (res.code === 0) {
    loginStore.sethomeJobAvtive(0)
    // IM相关操作
    logoutEaseMobIM()
    loginStore.sethomeCity1({})
    loginStore.sethomeCity2({})
    loginStore.sethomeCity3({})
    loginStore.setmyjobList([])
    clearStorageSync()
    uni.redirectTo({
      url: '/pages/login/index',
    })
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }
}
// 切换身份
const chanangeIdentity = () => {
  uni.navigateTo({
    url: '/setting/IdentitySwitching/index',
  })
}
</script>
<style scoped lang="scss">
.setting {
  padding: 0rpx 40rpx;
  // margin-bottom: 320rpx;
  .setting-list {
    padding: 30rpx 20rpx;
  }
}
.btn-flexd-red {
  height: 80rpx;
  line-height: 80rpx;
  color: #fff;
  text-align: center;
  background-color: #ff8080;
  border-radius: 30rpx;
}

.btn-flexd {
  position: fixed;
  bottom: 40rpx;
  left: 10%;
  width: 80%;
}
</style>
