<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="设置"></CustomNavBar>
    </template>
    <view class="setting">
      <view class="setting-list flex-between" @click="goAccountSecurity">
        <view class="text-32rpx">账号安全</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <view class="setting-list flex-between" @click="goResignation">
        <view class="text-32rpx">离开公司</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <view class="setting-list flex-between" @click="goDeregisterAccount">
        <view class="text-32rpx">注销公司</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})

// 账号安全
const goAccountSecurity = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/model/accountSecurity/index',
  })
}

// 离开公司
const goResignation = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/model/Resignation/index',
  })
}

// 注销账号
const goDeregisterAccount = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/model/deregisterAccount/index',
  })
}
</script>
<style scoped lang="scss">
.setting {
  padding: 0rpx 40rpx;
  // margin-bottom: 320rpx;
  .setting-list {
    padding: 30rpx 20rpx;
  }
}
</style>
